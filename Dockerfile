FROM python:3.12.6-alpine3.20 AS builder

# Install system dependencies needed for building
RUN apk add --no-cache \
    build-base \
    libffi-dev \
    openssl-dev \
    curl \
    git

# Install uv for fast dependency management
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Set working directory
WORKDIR /workdir

# Copy dependency files
COPY pyproject.toml uv.lock ./
COPY monitoring-python-module/ ./monitoring-python-module/

# Install dependencies using uv (much faster than pip)
RUN uv sync --frozen --no-dev --no-cache

FROM python:3.12.6-alpine3.20 AS runtime

# Create non-root user for security
RUN addgroup -g 1001 -S nonroot && \
    adduser -u 1001 -S nonroot -G nonroot

# Install only runtime system dependencies
RUN apk add --no-cache \
    libffi \
    openssl

# Set working directory
WORKDIR /workdir

# Copy virtual environment from builder stage
COPY --from=builder /workdir/.venv /workdir/.venv

# Copy application code
COPY app/ ./app/

# Create resources directory and set permissions
RUN mkdir -p /workdir/resources && \
    chown -R nonroot:nonroot /workdir

# Set environment variables
ENV PATH="/workdir/.venv/bin:$PATH" \
    PYTHONPATH="/workdir" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PYTHONIOENCODING=utf-8

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Switch to non-root user
USER nonroot

# Default command
CMD ["python", "app/main.py"]
