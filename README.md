- read message from a kafka queue, message indicates a s3 path for a file to download
- download the file from s3
- send call to transcription api
- upload the transcription to s3
- write into "client/day/job_id/file"
- send a message to a kafka queue indicating the transcription is done and where it is in s3


LEFT TODO:
- make ai agnostic
- get response content type from api (extension)
- not download locally (input and output if possible)
- test multiple workers
- test translation for generic worker
- error handling
