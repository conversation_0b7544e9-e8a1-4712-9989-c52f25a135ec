import json
import logging
import os
import signal
import sys
from uuid import uuid4
from types import FrameType
from typing import Optional

from config import config
from leptonai.client import Client, FileParam
from services.kafka_service import consumer, producer
from toolbox.logger import get_logger
from toolbox.s3 import S3Helper

kafka_logger = logging.getLogger("kafka")
kafka_logger.setLevel(logging.WARNING)


logger = get_logger(__name__)


def graceful_shutdown(signum: int, frame: Optional[FrameType]) -> None:
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    try:
        consumer.close()
        producer.close()
        logger.info("Connections closed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    sys.exit(0)


# Register signal handlers
signal.signal(signal.SIGTERM, graceful_shutdown)
signal.signal(signal.SIGINT, graceful_shutdown)


try:
    s3_helper = S3Helper(
        aws_access_key_id=config.S3_ACCESS_KEY_ID,
        aws_secret_access_key=config.S3_SECRET_ACCESS_KEY,
        endpoint_url=config.S3_ENDPOINT_URL,
        bucket_name=config.S3_BUCKET_NAME,
    )

    client = Client(config.LEPTON_API_URL)

    logger.info("Initialization succesful")
except Exception as e:
    logger.error(f"Initialization failed: {e}")
    sys.exit(1)

os.makedirs("resources", exist_ok=True)

for message in consumer:
    try:
        job_id, task_id = message.key.split("|")
        logger.info(f"Received request: {message.value}", extra={"job_id": job_id})
        input_s3_path = message.value.get("s3_key")
        _, _, input_job_id, filename = input_s3_path.split("/")

        rest

        basename, extension = os.path.splitext(filename)
        input_local_path = f"resources/{filename}"

        s3_helper.download_file(input_s3_path, input_local_path)

        result = client.run(input=FileParam(open(input_local_path, "rb")), **message.value.get("parameters"))

        output_local_path = input_local_path.replace(extension, "_result.json")
        output_s3_path = input_s3_path.replace(extension, "_result.json").replace(input_job_id, job_id)

        with open(output_local_path, "w") as f:
            f.write(json.dumps(result))

        s3_helper.upload_file(output_local_path, object_key=output_s3_path)

        response = {
            "status": "ok",
            "message": "",
            "s3_key": output_s3_path,
            "result_id": uuid4(),
        }

        producer.flush()
        logger.info("Request processed succesfully", extra={"job_id": job_id})
    except Exception as e:
        logger.error(f"Error processing request: {e}", extra={"job_id": "system"})
        response = {"status": "error", "message": str(e), "s3_key": None, "result_id": uuid4()}
    finally:
        consumer.commit()
        producer.send(config.KAFKA_OUTPUT_TOPIC, response, message.key)
