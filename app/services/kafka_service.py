from kafka import KafkaProducer, KafkaConsumer
import json

from config import config


producer = KafkaProducer(
    bootstrap_servers=config.KAFKA_BOOTSTRAP_SERVERS,
    value_serializer=lambda v: json.dumps(v).encode("utf-8"),  # JSON serialization
    key_serializer=lambda k: k.encode("utf-8") if k else None,  # Key serialization
    acks="all",  # Wait for all replicas to acknowledge
    retries=3,
    batch_size=16384,
    linger_ms=10,
    buffer_memory=33554432,
)

consumer = KafkaConsumer(
    config.KAFKA_INPUT_TOPIC,
    bootstrap_servers=config.KAFKA_BOOTSTRAP_SERVERS,
    group_id=config.KAFKA_GROUP_ID,
    auto_offset_reset="earliest",
    enable_auto_commit=False,
    value_deserializer=lambda m: json.loads(m.decode("utf-8")),  # JSON deserialization
    key_deserializer=lambda k: k.decode("utf-8") if k else None,
)
