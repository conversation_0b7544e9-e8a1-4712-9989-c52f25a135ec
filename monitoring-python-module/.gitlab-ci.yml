stages:
  - build
  - publish

variables:
  DOCKERFILE: Dockerfile
  CONTEXT: .
  PYPI_URL: "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/pypi"
  TWINE_USERNAME: "gitlab-ci-token"
  TWINE_PASSWORD: "${CI_JOB_TOKEN}"

# Only run this pipeline on tag pushes
workflow:
  rules:
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

build-docker:
  stage: build
  image: gcr.io/kaniko-project/executor:v1.23.2-debug
  script:
    - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile $DOCKERFILE --destination ${CI_REGISTRY}/${CI_PROJECT_PATH}:${CI_COMMIT_TAG}

build-package:
  stage: build
  image: python:3.12-slim
  script:
    - pip install build
    - python -m build
  artifacts:
    paths:
      - dist/
    expire_in: 1 day

publish-package:
  stage: publish
  image: python:3.12-slim
  needs:
    - build-package
  script:
    - pip install twine
    - twine upload --verbose --repository-url "$PYPI_URL" -u "$TWINE_USERNAME" -p "$TWINE_PASSWORD" dist/*
