# Python Monitoring Toolbox

A comprehensive Python monitoring library with SSO authentication for facturation (billing) purposes. This toolbox provides standardized logging with facturation capabilities and SSO integration for authentication and authorization.

## Features

- **Advanced Logging System**
  - Custom facturation level for billing-related events
  - Configurable formatters and filters
  - Structured logging with contextual information
  - Endpoint filtering for health checks

- **SSO Authentication**
  - Seamless integration with Keycloak
  - Support for multiple OAuth2 grant types
  - Token verification and validation
  - User/client metadata extraction

## Installation

### From PyPI (Recommended)

```bash
pip install python-monitoring
```

### From Source

```bash
git clone https://vor.hexaglobe.net/ai/monitoring-python-module.git
cd monitoring-python-module
pip install .
```

### Using Docker

```bash
docker pull registry.gitlab.com/path/to/monitoring-python-module:latest
docker run -it registry.gitlab.com/path/to/monitoring-python-module python
```

## Quick Start

### Basic Usage

```python
from toolbox.logger import get_logger
from toolbox.sso import SSOHelper

# Initialize logger
logger = get_logger(__name__)

# Log standard messages
logger.info("Application started")
logger.debug("Debug information")
logger.warning("Warning message")
logger.error("Error occurred", extra={"job_id": "job-123"})

# Initialize SSO helper
sso_helper = SSOHelper(
    keycloak_server="https://keycloak.example.com",
    realm="your-realm",
    client_id="your-client-id",
    client_secret="your-client-secret",
    grant_type="client_credentials"
)

# Authenticate and get token
token_data = sso_helper.login()

# Log facturation events with token data
logger.facturation(
    "Service usage recorded", 
    token_data["decoded"],
    cf1="42minutes",  # Custom field 1
    cf2="5200",       # Custom field 2
    cf3="premium"     # Custom field 3
)
```

## Modules

### Logger Module

The logger module provides a standardized logging system with support for facturation events.

#### Features

- Custom facturation log level (between INFO and WARNING)
- Contextual logging with job IDs
- Configurable formatters and filters
- Endpoint filtering for health checks

#### Usage

```python
from toolbox.logger import get_logger

# Get a logger instance
logger = get_logger(__name__)

# Standard logging
logger.info("Standard log message")
logger.debug("Debug information")
logger.warning("Warning message")
logger.error("Error occurred", extra={"job_id": "job-123"})

# Facturation logging (for billing events)
logger.facturation(
    "Service usage recorded",  # Message
    decoded_token,             # Token data
    "42minutes",               # Custom field 1
    5200,                      # Custom field 2
    "premium"                  # Custom field 3
)
```

### SSO Module

The SSO module provides authentication and authorization capabilities using Keycloak.

#### Features

- Support for multiple OAuth2 grant types
- Token verification and validation
- User/client metadata extraction

#### Usage

```python
from toolbox.sso import SSOHelper

# Initialize the SSO helper
sso_helper = SSOHelper(
    keycloak_server="https://keycloak.example.com",
    realm="your-realm",
    client_id="your-client-id",
    client_secret="your-client-secret",
    grant_type="client_credentials"  # Or "password" for username/password auth
)

# Client credentials authentication
token_data = sso_helper.login()

# Password authentication
token_data = sso_helper.login(
    username="your-username",
    password="your-password"
)

# Verify a token
decoded_token = sso_helper.verify_token(token)

# Extract user metadata
user_info = sso_helper.extract_metadata(decoded_token)
```

## Configuration

### Environment Variables

Create a `.env` file based on the provided `.env.template`:

```
password=your-client-secret
```

## Development

### Prerequisites

- Python 3.12 or higher
- pip

### Setup Development Environment

```bash
# Clone the repository
git clone https://vor.hexaglobe.net/ai/monitoring-python-module.git
cd monitoring-python-module

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e .
```

### Running Tests

```bash
# Run tests
pytest
```

## Contact

For support or inquiries, contact: <EMAIL>
