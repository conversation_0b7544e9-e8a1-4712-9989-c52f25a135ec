#!/bin/bash

IMAGE1="$1"
IMAGE2="$2"
EXTENSION=".py"  # Change to whatever file types you care about

# Temporary directories
TMPDIR1="img1_fs"
TMPDIR2="img2_fs"

# Containers
CID1=$(docker create "$IMAGE1")
CID2=$(docker create "$IMAGE2")

mkdir -p "$TMPDIR1" "$TMPDIR2"

# Export and extract both
docker export "$CID1" | tar -xf - -C "$TMPDIR1"
docker export "$CID2" | tar -xf - -C "$TMPDIR2"

# Clean up containers
docker rm "$CID1" "$CID2" > /dev/null

# Compare Python files only
echo "🔍 Comparing Python files in $IMAGE1 vs $IMAGE2 ..."
diff -r --brief --exclude="*.pyc" --exclude="__pycache__" \
     <(find "$TMPDIR1" -name "*$EXTENSION" | sort) \
     <(find "$TMPDIR2" -name "*$EXTENSION" | sort)

# Or show detailed diffs
echo
echo "📄 File content differences:"
comm -12 <(find "$TMPDIR1" -name "*$EXTENSION" | sed "s|$TMPDIR1/||" | sort) \
         <(find "$TMPDIR2" -name "*$EXTENSION" | sed "s|$TMPDIR2/||" | sort) | while read -r file; do
    echo "--- $file ---"
    diff -u "$TMPDIR1/$file" "$TMPDIR2/$file"
done

# Clean up
rm -rf "$TMPDIR1" "$TMPDIR2"

